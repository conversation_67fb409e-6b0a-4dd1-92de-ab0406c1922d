"""
改进版 MCP 反馈收集器
解决原版本的稳定性和兼容性问题
"""

import io
import base64
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import threading
import queue
import time
import sys
import os
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageTk, ImageGrab
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.warning("PIL not available, image features will be limited")

try:
    from mcp.server.fastmcp import FastMCP
    from mcp.server.fastmcp.utilities.types import Image as MCPImage
    from mcp.types import TextContent
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logger.error("MCP not available, please install mcp package")

# 配置
DEFAULT_DIALOG_TIMEOUT = 300
DIALOG_TIMEOUT = int(os.getenv("MCP_DIALOG_TIMEOUT", DEFAULT_DIALOG_TIMEOUT))

class ImprovedFeedbackDialog:
    """改进的反馈对话框，解决线程和资源管理问题"""
    
    def __init__(self, work_summary: str = "", timeout_seconds: int = DIALOG_TIMEOUT):
        self.result_queue = queue.Queue()
        self.root = None
        self.work_summary = work_summary
        self.timeout_seconds = timeout_seconds
        self.selected_images = []
        self.is_closed = False
        self._cleanup_done = False
        
    def show_dialog(self):
        """显示对话框并返回结果"""
        if not self._check_gui_available():
            return self._fallback_text_input()
            
        # 在主线程中运行 GUI
        if threading.current_thread() is threading.main_thread():
            return self._run_dialog_sync()
        else:
            return self._run_dialog_async()
    
    def _check_gui_available(self):
        """检查 GUI 是否可用"""
        try:
            # 检查是否在 SSH 或无显示环境
            if os.getenv('SSH_CLIENT') or os.getenv('SSH_TTY'):
                logger.warning("SSH environment detected, GUI may not work")
                return False
                
            if not os.getenv('DISPLAY') and sys.platform.startswith('linux'):
                logger.warning("No DISPLAY environment variable, GUI unavailable")
                return False
                
            # 尝试创建测试窗口
            test_root = tk.Tk()
            test_root.withdraw()
            test_root.destroy()
            return True
        except Exception as e:
            logger.error(f"GUI not available: {e}")
            return False
    
    def _fallback_text_input(self):
        """GUI 不可用时的文本输入回退方案"""
        print(f"\n{'='*60}")
        print("🎯 工作完成汇报与反馈收集")
        print(f"{'='*60}")
        
        if self.work_summary:
            print(f"\n📋 AI工作完成汇报：")
            print(f"{self.work_summary}")
        
        print(f"\n💬 请输入您的反馈（按 Enter 两次结束输入）：")
        
        lines = []
        empty_count = 0
        
        try:
            while empty_count < 2:
                line = input()
                if line.strip() == "":
                    empty_count += 1
                else:
                    empty_count = 0
                lines.append(line)
        except (EOFError, KeyboardInterrupt):
            return None
            
        feedback_text = "\n".join(lines).strip()
        
        if not feedback_text:
            return None
            
        return [{
            "type": "text",
            "text": f"用户文字反馈：{feedback_text}\n提交时间：{datetime.now().isoformat()}"
        }]
    
    def _run_dialog_sync(self):
        """同步运行对话框（主线程）"""
        try:
            self._create_dialog()
            self.root.mainloop()
            return self._get_result()
        except Exception as e:
            logger.error(f"Dialog error: {e}")
            return None
        finally:
            self._cleanup()
    
    def _run_dialog_async(self):
        """异步运行对话框（子线程）"""
        dialog_thread = threading.Thread(target=self._run_dialog_sync)
        dialog_thread.daemon = True
        dialog_thread.start()
        
        try:
            result = self.result_queue.get(timeout=self.timeout_seconds)
            return result
        except queue.Empty:
            logger.warning(f"Dialog timeout after {self.timeout_seconds} seconds")
            return None
        finally:
            self._cleanup()
    
    def _create_dialog(self):
        """创建对话框界面"""
        self.root = tk.Tk()
        self.root.title("🎯 工作完成汇报与反馈收集")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # 设置窗口属性
        self.root.attributes('-topmost', True)  # 置顶
        self.root.lift()  # 提升到前台
        self.root.focus_force()  # 强制获取焦点
        
        # 居中显示
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"700x600+{x}+{y}")
        
        # 处理窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)
        
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = tk.Frame(self.root, bg="#f5f5f5")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="🎯 工作完成汇报与反馈收集",
            font=("Arial", 16, "bold"),
            bg="#f5f5f5",
            fg="#2c3e50"
        )
        title_label.pack(pady=(0, 20))
        
        # 工作汇报区域
        if self.work_summary:
            report_frame = tk.LabelFrame(
                main_frame,
                text="📋 AI工作完成汇报",
                font=("Arial", 12, "bold"),
                bg="#ffffff",
                fg="#34495e"
            )
            report_frame.pack(fill=tk.X, pady=(0, 15))
            
            report_text = tk.Text(
                report_frame,
                height=4,
                wrap=tk.WORD,
                bg="#ecf0f1",
                fg="#2c3e50",
                font=("Arial", 10),
                state=tk.DISABLED
            )
            report_text.pack(fill=tk.X, padx=10, pady=10)
            
            report_text.config(state=tk.NORMAL)
            report_text.insert(tk.END, self.work_summary)
            report_text.config(state=tk.DISABLED)
        
        # 用户反馈区域
        feedback_frame = tk.LabelFrame(
            main_frame,
            text="💬 您的反馈",
            font=("Arial", 12, "bold"),
            bg="#ffffff",
            fg="#34495e"
        )
        feedback_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        self.text_widget = scrolledtext.ScrolledText(
            feedback_frame,
            height=8,
            wrap=tk.WORD,
            font=("Arial", 10),
            bg="#ffffff",
            fg="#2c3e50"
        )
        self.text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 按钮区域
        button_frame = tk.Frame(main_frame, bg="#f5f5f5")
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        submit_btn = tk.Button(
            button_frame,
            text="✅ 提交反馈",
            command=self._submit_feedback,
            font=("Arial", 12, "bold"),
            bg="#27ae60",
            fg="white",
            width=15,
            height=2
        )
        submit_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        cancel_btn = tk.Button(
            button_frame,
            text="❌ 取消",
            command=self._cancel,
            font=("Arial", 12),
            bg="#95a5a6",
            fg="white",
            width=15,
            height=2
        )
        cancel_btn.pack(side=tk.LEFT)
        
        # 设置焦点
        self.text_widget.focus_set()
    
    def _submit_feedback(self):
        """提交反馈"""
        text_content = self.text_widget.get(1.0, tk.END).strip()
        
        if not text_content:
            messagebox.showwarning("警告", "请输入反馈内容")
            return
        
        result = [{
            "type": "text", 
            "text": f"用户文字反馈：{text_content}\n提交时间：{datetime.now().isoformat()}"
        }]
        
        self.result_queue.put(result)
        self._close_dialog()
    
    def _cancel(self):
        """取消操作"""
        self.result_queue.put(None)
        self._close_dialog()
    
    def _on_window_close(self):
        """窗口关闭事件处理"""
        self._cancel()
    
    def _close_dialog(self):
        """关闭对话框"""
        if self.root and not self.is_closed:
            self.is_closed = True
            try:
                self.root.quit()
                self.root.destroy()
            except:
                pass
    
    def _get_result(self):
        """获取结果"""
        try:
            return self.result_queue.get_nowait()
        except queue.Empty:
            return None
    
    def _cleanup(self):
        """清理资源"""
        if not self._cleanup_done:
            self._cleanup_done = True
            self._close_dialog()
            # 清理图片资源
            self.selected_images.clear()

# MCP 服务器实现
if MCP_AVAILABLE:
    mcp = FastMCP("改进版反馈收集器")
    
    @mcp.tool()
    def collect_feedback(work_summary: str = "", timeout_seconds: int = DIALOG_TIMEOUT) -> list:
        """
        收集用户反馈的交互式工具
        
        Args:
            work_summary: AI完成的工作内容汇报
            timeout_seconds: 对话框超时时间（秒）
            
        Returns:
            包含用户反馈内容的列表
        """
        try:
            dialog = ImprovedFeedbackDialog(work_summary, timeout_seconds)
            result = dialog.show_dialog()
            
            if result is None:
                raise Exception("用户取消了反馈提交或操作超时")
            
            return result
            
        except Exception as e:
            logger.error(f"Feedback collection failed: {e}")
            raise Exception(f"反馈收集失败: {str(e)}")
    
    def main():
        """主入口点"""
        try:
            logger.info("Starting improved MCP feedback collector...")
            mcp.run()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        except Exception as e:
            logger.error(f"Server error: {e}")
            raise

else:
    def main():
        print("MCP package not available. Please install with: pip install mcp")
        sys.exit(1)

if __name__ == "__main__":
    main()
