# 🎯 改进版 MCP 反馈收集器

一个改进的 Model Context Protocol (MCP) 服务器，解决了原版本的稳定性和兼容性问题，为 AI 助手提供更可靠的交互式用户反馈收集功能。

[![Version](https://img.shields.io/badge/version-2.1.0-blue)](https://github.com/improved-mcp-feedback-collector)
[![Python](https://img.shields.io/badge/python-3.8+-green)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-orange)](LICENSE)

## ✨ 改进特性

### 🔧 解决的问题
- ✅ **修复连接关闭错误** - 解决 Mac 系统 `connection closed -32000` 问题
- ✅ **改进线程管理** - 防止阻塞其他 MCP 服务
- ✅ **增强稳定性** - 支持多次对话反馈收集
- ✅ **跨平台兼容** - 更好的 Windows/Mac/Linux 支持
- ✅ **SSH 环境支持** - 无 GUI 环境下的文本回退方案
- ✅ **窗口管理优化** - 自动置顶和居中显示

### 🚀 新增功能
- 🎨 **智能 GUI 检测** - 自动检测 GUI 可用性
- 📝 **文本回退模式** - SSH 环境下的命令行交互
- 🔄 **资源自动清理** - 防止内存泄漏和资源残留
- ⚡ **快速响应** - 优化的线程同步机制
- 🛡️ **错误恢复** - 完善的异常处理和恢复机制

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 Python 包管理工具
pip install uvx

# 或者直接安装
pip install improved-mcp-feedback-collector
```

### 2. 配置 Claude Desktop

在 `claude_desktop_config.json` 中添加：

```json
{
  "mcpServers": {
    "improved-mcp-feedback-collector": {
      "command": "python",
      "args": ["improved_mcp_feedback_collector.py"],
      "env": {
        "PYTHONIOENCODING": "utf-8",
        "MCP_DIALOG_TIMEOUT": "600"
      }
    }
  }
}
```

### 3. 重启 Claude Desktop

配置完成后重启 Claude Desktop 即可使用。

## 🛠️ 核心功能

### collect_feedback()

改进的反馈收集工具，支持：
- 🖥️ GUI 模式（有显示环境）
- 📝 文本模式（SSH/无显示环境）
- ⏱️ 可配置超时时间
- 🔄 多次调用支持

```python
# AI调用示例
result = collect_feedback("我已经完成了代码优化工作...")
```

## 🖼️ 界面预览

### GUI 模式
```
🎯 工作完成汇报与反馈收集
┌─────────────────────────────────────────┐
│ 📋 AI工作完成汇报                        │
│ ┌─────────────────────────────────────┐ │
│ │ [AI汇报的工作内容显示在这里]         │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│ 💬 您的反馈                              │
│ ┌─────────────────────────────────────┐ │
│ │ [多行文本输入区域]                   │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘

[✅ 提交反馈]  [❌ 取消]
```

### 文本模式（SSH环境）
```
============================================================
🎯 工作完成汇报与反馈收集
============================================================

📋 AI工作完成汇报：
我已经完成了代码优化工作...

💬 请输入您的反馈（按 Enter 两次结束输入）：
[用户输入反馈内容]
```

## ⚙️ 配置说明

### 环境变量

- `MCP_DIALOG_TIMEOUT`: 对话框等待时间（秒）
  - 默认：300秒（5分钟）
  - 建议：600秒（10分钟）
  - 复杂操作：1200秒（20分钟）

### 平台特定配置

#### Windows
```json
{
  "command": "python",
  "args": ["improved_mcp_feedback_collector.py"]
}
```

#### Mac/Linux
```json
{
  "command": "python3", 
  "args": ["improved_mcp_feedback_collector.py"]
}
```

#### SSH 环境
自动检测并切换到文本模式，无需特殊配置。

## 🔧 技术架构

### 核心改进
- **线程安全** - 使用队列和锁机制确保线程安全
- **资源管理** - 自动清理 GUI 资源和内存
- **异常处理** - 完善的错误捕获和恢复机制
- **平台检测** - 智能检测运行环境和 GUI 可用性

### 技术栈
- **MCP框架**: FastMCP
- **GUI**: tkinter（可选）
- **多线程**: threading + queue
- **图片处理**: Pillow（可选）
- **日志**: Python logging

## 📝 更新日志

### v2.1.0 (2025-01-XX)

#### 🔧 问题修复
- 修复 Mac 系统连接关闭错误
- 解决其他 MCP 服务失效问题
- 修复只能对话一次的限制
- 改进窗口置顶和居中显示

#### 🚀 新增功能
- SSH 环境文本回退模式
- 智能 GUI 可用性检测
- 改进的资源管理
- 增强的错误处理

#### 🎨 用户体验
- 优化界面响应速度
- 改进窗口管理
- 简化配置流程

## 💡 使用场景

- ✅ AI 完成任务后收集用户评价
- ✅ 远程服务器环境下的反馈收集
- ✅ 获取用户对代码/设计的意见
- ✅ 收集 bug 报告和改进建议
- ✅ 多轮对话中的持续反馈

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/improved-mcp-feedback-collector.git
cd improved-mcp-feedback-collector

# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest

# 代码格式化
black .
isort .
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**让 AI 与用户的交互更稳定可靠！** 🎯
