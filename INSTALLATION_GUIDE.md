# 📦 改进版 MCP 反馈收集器安装指南

## 🚀 快速安装

### 方法一：直接使用（推荐）

1. **下载文件**
   ```bash
   # 下载主文件
   wget https://raw.githubusercontent.com/your-repo/improved_mcp_feedback_collector.py
   
   # 或者克隆整个项目
   git clone https://github.com/your-repo/improved-mcp-feedback-collector.git
   cd improved-mcp-feedback-collector
   ```

2. **安装依赖**
   ```bash
   pip install mcp pillow
   ```

3. **配置 Claude Desktop**
   
   找到 Claude Desktop 配置文件：
   - **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
   - **Mac**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Linux**: `~/.config/Claude/claude_desktop_config.json`
   
   添加配置：
   ```json
   {
     "mcpServers": {
       "improved-mcp-feedback-collector": {
         "command": "python",
         "args": ["/path/to/improved_mcp_feedback_collector.py"],
         "env": {
           "PYTHONIOENCODING": "utf-8",
           "MCP_DIALOG_TIMEOUT": "600"
         }
       }
     }
   }
   ```

### 方法二：使用 uvx（如果可用）

```bash
# 安装 uvx
pip install uvx

# 使用 uvx 运行
uvx improved-mcp-feedback-collector
```

## 🔧 平台特定配置

### Windows 配置

```json
{
  "mcpServers": {
    "improved-mcp-feedback-collector": {
      "command": "python",
      "args": ["C:\\path\\to\\improved_mcp_feedback_collector.py"],
      "env": {
        "PYTHONIOENCODING": "utf-8",
        "MCP_DIALOG_TIMEOUT": "600"
      }
    }
  }
}
```

**注意事项：**
- 使用完整路径
- 路径中的反斜杠需要转义或使用正斜杠
- 确保 Python 在 PATH 中

### Mac 配置

```json
{
  "mcpServers": {
    "improved-mcp-feedback-collector": {
      "command": "python3",
      "args": ["/Users/<USER>/improved_mcp_feedback_collector.py"],
      "env": {
        "PYTHONIOENCODING": "utf-8",
        "MCP_DIALOG_TIMEOUT": "600"
      }
    }
  }
}
```

**注意事项：**
- 使用 `python3` 而不是 `python`
- 确保文件有执行权限：`chmod +x improved_mcp_feedback_collector.py`

### Linux 配置

```json
{
  "mcpServers": {
    "improved-mcp-feedback-collector": {
      "command": "python3",
      "args": ["/home/<USER>/improved_mcp_feedback_collector.py"],
      "env": {
        "PYTHONIOENCODING": "utf-8",
        "MCP_DIALOG_TIMEOUT": "600",
        "DISPLAY": ":0"
      }
    }
  }
}
```

**注意事项：**
- 设置 DISPLAY 环境变量
- 对于 SSH 环境，GUI 会自动切换到文本模式

## 🧪 测试安装

### 1. 基础测试

```bash
# 运行测试脚本
python test_improved_collector.py
```

### 2. 交互式测试

```bash
# 运行交互式测试
python test_improved_collector.py --interactive
```

### 3. 手动测试

```bash
# 直接运行 MCP 服务器
python improved_mcp_feedback_collector.py
```

## 🔍 故障排除

### 常见问题

#### 1. "MCP package not available"
```bash
# 解决方案：安装 MCP
pip install mcp
```

#### 2. "PIL not available"
```bash
# 解决方案：安装 Pillow
pip install pillow
```

#### 3. GUI 不显示（Linux/SSH）
- **现象**: 在 SSH 环境下 GUI 不显示
- **解决方案**: 改进版会自动切换到文本模式，无需额外配置

#### 4. 窗口不置顶
- **现象**: 对话框被其他窗口遮挡
- **解决方案**: 改进版已修复此问题，窗口会自动置顶

#### 5. 连接关闭错误
- **现象**: Mac 系统出现 "connection closed -32000"
- **解决方案**: 改进版已修复线程管理问题

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

或设置环境变量：
```bash
export PYTHONPATH=/path/to/project
export MCP_DEBUG=1
```

## 📋 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MCP_DIALOG_TIMEOUT` | 300 | 对话框超时时间（秒） |
| `PYTHONIOENCODING` | utf-8 | Python 编码设置 |
| `MCP_DEBUG` | 0 | 调试模式开关 |

### 超时时间建议

- **快速反馈**: 60-120 秒
- **一般使用**: 300-600 秒
- **复杂任务**: 900-1200 秒

## 🔄 升级指南

### 从原版本升级

1. **备份配置**
   ```bash
   cp claude_desktop_config.json claude_desktop_config.json.backup
   ```

2. **更新配置**
   - 将 `mcp-feedback-collector` 改为 `improved-mcp-feedback-collector`
   - 更新文件路径

3. **重启 Claude Desktop**

### 版本兼容性

- ✅ 兼容原版本的所有功能
- ✅ 向后兼容现有配置
- ✅ 无需修改 AI 调用代码

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: 检查 Claude Desktop 的日志输出
2. **运行测试**: 使用测试脚本诊断问题
3. **检查环境**: 确认 Python 和依赖包版本
4. **提交 Issue**: 在 GitHub 上报告问题

---

**安装成功后，您就可以享受更稳定可靠的反馈收集体验了！** 🎉
