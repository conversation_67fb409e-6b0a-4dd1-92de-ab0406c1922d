#!/usr/bin/env python3
"""
测试改进版 MCP 反馈收集器
"""

import sys
import os
import time
import threading
from improved_mcp_feedback_collector import ImprovedFeedbackDialog

def test_gui_availability():
    """测试 GUI 可用性检测"""
    print("🔍 测试 GUI 可用性检测...")
    
    dialog = ImprovedFeedbackDialog()
    gui_available = dialog._check_gui_available()
    
    print(f"GUI 可用性: {'✅ 可用' if gui_available else '❌ 不可用'}")
    print(f"当前环境: {sys.platform}")
    print(f"SSH 环境: {'是' if os.getenv('SSH_CLIENT') or os.getenv('SSH_TTY') else '否'}")
    print(f"DISPLAY: {os.getenv('DISPLAY', '未设置')}")
    
    return gui_available

def test_text_fallback():
    """测试文本回退模式"""
    print("\n📝 测试文本回退模式...")
    print("注意：这将启动文本输入模式，请输入一些文本并按两次 Enter 结束")
    
    # 临时设置环境变量模拟 SSH 环境
    original_ssh = os.getenv('SSH_CLIENT')
    os.environ['SSH_CLIENT'] = 'test'
    
    try:
        dialog = ImprovedFeedbackDialog("测试工作汇报：代码优化完成", timeout_seconds=30)
        result = dialog.show_dialog()
        
        if result:
            print("✅ 文本回退模式测试成功")
            print(f"收到反馈: {result[0]['text'][:100]}...")
        else:
            print("❌ 文本回退模式测试失败或超时")
            
    finally:
        # 恢复环境变量
        if original_ssh:
            os.environ['SSH_CLIENT'] = original_ssh
        else:
            os.environ.pop('SSH_CLIENT', None)

def test_gui_dialog():
    """测试 GUI 对话框"""
    print("\n🖥️ 测试 GUI 对话框...")
    
    if not test_gui_availability():
        print("⚠️ GUI 不可用，跳过 GUI 测试")
        return
    
    print("将显示 GUI 对话框，请在 10 秒内提交反馈...")
    
    dialog = ImprovedFeedbackDialog(
        "测试工作汇报：\n1. 完成了代码重构\n2. 优化了性能\n3. 修复了 bug",
        timeout_seconds=10
    )
    
    result = dialog.show_dialog()
    
    if result:
        print("✅ GUI 对话框测试成功")
        print(f"收到反馈: {result[0]['text'][:100]}...")
    else:
        print("❌ GUI 对话框测试失败或超时")

def test_thread_safety():
    """测试线程安全性"""
    print("\n🔄 测试线程安全性...")
    
    results = []
    
    def worker(worker_id):
        try:
            dialog = ImprovedFeedbackDialog(f"工作线程 {worker_id} 的汇报", timeout_seconds=5)
            # 模拟快速返回结果
            dialog.result_queue.put([{"type": "text", "text": f"线程 {worker_id} 的反馈"}])
            result = dialog._get_result()
            results.append((worker_id, result is not None))
        except Exception as e:
            results.append((worker_id, False))
            print(f"线程 {worker_id} 出错: {e}")
    
    # 创建多个线程
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    success_count = sum(1 for _, success in results if success)
    print(f"线程安全测试: {success_count}/{len(results)} 成功")
    
    if success_count == len(results):
        print("✅ 线程安全测试通过")
    else:
        print("❌ 线程安全测试失败")

def test_resource_cleanup():
    """测试资源清理"""
    print("\n🧹 测试资源清理...")
    
    # 创建多个对话框实例
    dialogs = []
    for i in range(5):
        dialog = ImprovedFeedbackDialog(f"测试对话框 {i}")
        dialogs.append(dialog)
    
    # 清理所有对话框
    for dialog in dialogs:
        dialog._cleanup()
    
    print("✅ 资源清理测试完成")

def main():
    """主测试函数"""
    print("🎯 改进版 MCP 反馈收集器测试")
    print("=" * 50)
    
    try:
        # 基础功能测试
        test_gui_availability()
        test_resource_cleanup()
        test_thread_safety()
        
        # 交互式测试（可选）
        if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
            print("\n🎮 交互式测试模式")
            
            choice = input("\n选择测试模式:\n1. GUI 测试\n2. 文本测试\n请输入选择 (1/2): ").strip()
            
            if choice == "1":
                test_gui_dialog()
            elif choice == "2":
                test_text_fallback()
            else:
                print("无效选择")
        
        print("\n✅ 所有测试完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    main()
